@extends('backend.content.index')

@section('dynamicCSS')
	<link rel="stylesheet" href="{{url("inc/DataTables/datatables.css")}}?v=3">
@endsection

@section('postscripts')
	<script src="{{ asset('inc/DataTables/datatables.js') }}"></script>
	<script src="{{ asset('inc/fullcalendar-6.1.15/dist/index.global.min.js') }}"></script>
	<script src="{{ asset('inc/initDatatable.js') }}"></script>
	<script src="{{ asset('inc/buss/planungskalender.js') }}"></script>

@endsection


@section('card-content')

	@if ($feiertagName)
		<div class="alert alert-warning" role="alert">
			<strong>Achtung:</strong> Der {{ \Carbon\Carbon::parse($selectedDate)->format('d.m.Y') }} ist ein Feiertag ({{ $feiertagName }}).
		</div>
	@endif

	<div class="row">
		<div class="col-12 d-flex justify-content-end align-items-end mb-3">
			<div class="d-flex align-items-center ">
				@if(config('beilagenkalkulator.env') == 'krone')
					<button class="btn btn-danger btn-sm text-white me-2 fw-bolder fa-lg" onclick="switchToKurier()">
						<i class="fad fa-repeat-alt me-2"></i>Kurier
					</button>
				@else
					<button class="btn btn-danger btn-sm text-white me-2" onclick="switchToKrone()">
						<i class="fad fa-repeat-alt me-2"></i> Krone
					</button>
				@endif
				@if(request()->segment(4) == 'storno')
					<a href="{{ route('backend.beilagenrechner.planungskalender') }}" class="btn btn-dark me-2 btn-sm">
						<i class="fa-solid fa-eye-slash me-1"></i>Stornierte Aufträge ausblenden
					</a>
				@else
					<a href="{{ route('backend.beilagenrechner.planungskalender', 'storno') }}" class="btn btn-outline-dark me-2 btn-sm">
						<i class="fa-solid fa-eye me-1"></i>Stornierte Aufträge einblenden
					</a>
				@endif
				<div class="me-2">
					<select id="objekt" class="form-control form-control-sm form-select" name="objekt" onchange="this.form.submit()">
						<option value="all" {{ $selectedObjekt == 'all' ? 'selected' : '' }}>Alle Objekte</option>
						@foreach(config('backend.buss.options.Objekt_'.env('MEDIA')) as $key => $option)
							<option value="{{ $key }}" {{--{{ $selectedObjekt == $key ? 'selected' : '' }}--}}
								  @if ($selectedObjekt == $key || (empty($selectedObjekt) && $key === env('MEDIA')))
                                    selected
                                @endif>
                                      {{ $option }}
                            </option>
						@endforeach
					</select>
				</div>
				<div class="me-2">
					<select id="beilagenart" class="form-control form-control-sm form-select" name="beilagenart">
						<option selected value="all">Alle Beilagenarten</option>
						@foreach(config('backend.buss.options.Beilagenarten') as $key => $option)
							<option value="{{$key}}" {{(request('beilagenart') == $key) ? 'selected' : ''}}>{{$option}}</option>
						@endforeach
					</select>
				</div>
				<div class="me-2">
					<input type="date" id="datum" name="datum" class="form-control form-control-sm" value="{{ request('datum') ?? $selectedDate}}">
				</div>
				<a class="btn btn-success btn-sm text-white" href="{{route('backend.beilagenrechner.planungskalender.create',[
                                                                                                        'objekt' => request('objekt') ?? null,
                                                                                                        'datum' => request('datum') ?? null,
                                                                                                    ])}}">
					<i class="fa-solid fa-plus me-1"></i>Neue Reservierung
				</a>
			</div>
		</div>
	</div>
	<div class="row mb-2">
		<div class="col-12">

			@if(isset($tagesnotiz))
				<div class="alert alert-info mb-0 py-2 px-3 position-relative" role="alert" style="border-left: 5px solid #198754;">
					<strong>Tagesmemo für {{ \Carbon\Carbon::parse($selectedDate)->format('d.m.Y') }}:</strong>
					<div class="mt-1" style="word-wrap: break-word; white-space: normal;">
						{!! nl2br(e($tagesnotiz->notiz)) !!}
					</div>
					<button type="button" class="btn btn-sm btn-outline-dark position-absolute"
					        style="right: 10px; top: 6px;"
					        id="editTagesnotizBtn">
						<i class="fa-solid fa-comment me-1"></i>Tagesmemo
					</button>
				</div>
			@else
				<button type="button" class="btn btn-outline-info" id="addTagesnotizBtn">
					<i class="fa-solid fa-plus me-1"></i>Tagesmemo hinzufügen
				</button>
			@endif


		</div>
	</div>

	<div class="modal fade" id="tagesnotizModal" tabindex="-1" aria-labelledby="tagesnotizModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header bg-info text-white">
					<h5 class="modal-title" id="tagesnotizModalLabel">Tagesmemo für {{ \Carbon\Carbon::parse($selectedDate)->format('d.m.Y') }} erstellen</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<form id="tagesnotizForm" action="{{ route('backend.beilagenrechner.planungskalender.tagesnotiz') }}" method="POST">
						@csrf
						<input type="hidden" name="tag" value="{{ $selectedDate }}">
						<div class="mb-3">
							<textarea class="form-control" id="notiz" name="notiz" rows="5">{!! $tagesnotiz->notiz ?? '' !!}</textarea>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen</button>
					<button type="button" class="btn btn-primary text-white" id="saveTagesnotizBtn">Speichern</button>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-12 ">
			<div class="card">
				<div class="card-header">
					Reservierungen
				</div>
				<div class="card-body">
					<table id="tarifeTable" class="table table-bordered table-striped">
						<thead>
						<tr>
							<th width="30px">#</th>
							<th width="100px">Firmenname</th>

							<th>FIX</th>
							<th width="200px">Notiz</th>
							{{--<th>BI</th>--}}
							<th width="550px">Bundesländer <br>
								<small>(🔵Gebucht / 🔶 Reserviert)</small>
							</th>
                            <th width="50px">Format</th>
                            <th width="50px">Gewicht</th>
                            <th width="50px">Schlager</th>
							<th width="50px">Stück</th>
							<th width="50px">Branche</th>

							{{-- <th>tat.BDL</th>--}}
							<th width="200px"></th>
						</tr>
						</thead>
						<tbody>
						@foreach($auftraege as $auftrag)
							<tr class="{{ $auftrag->deleted_at ? 'table-danger' : '' }}">
								<td>{{$auftrag->id}}</td>
								<td>{{Str::limit($auftrag->kundenname)}}</td>

								<td>{{$auftrag->auftrag ? 'JA' : 'NEIN'}}</td>
								<td>{{Str::limit($auftrag->memo)}}</td>
								<td>

									@php
										$tatBundeslaender = [];
                                        if ($auftrag->auftrag && !empty($auftrag->auftrag->tat_bundeslaender)) {
                                            $tatBundeslaender = json_decode($auftrag->auftrag->tat_bundeslaender, true) ?? [];
                                        }

                                        $reservierteBundeslaender = [];
                                        if (!empty($auftrag->bundeslaender)) {
                                            $reservierteBundeslaender = json_decode($auftrag->bundeslaender, true) ?? [];
                                        }

                                        $bundeslaender = array_values(config('backend.buss.options.bundeslaender'));
									@endphp

									@foreach($bundeslaender as $key => $land)

										@php
											if (!empty($tatBundeslaender)) {

                                                $badgeClass = in_array($land, $tatBundeslaender)
                                                    ? 'bg-primary text-white'
                                                    : 'bg-secondary text-white';
                                            } else {




                                                $badgeClass = in_array($land, $reservierteBundeslaender)
                                                    ? 'bg-warning text-dark'
                                                    : 'bg-secondary text-white';
                                            }
										@endphp
										<span class="badge {{ $badgeClass }} m-1 px-2 py-1 fs-7 rounded-pill text-uppercase">
        {{ $land }}
    </span>
									@endforeach

								</td>
								{{--</td>--}}

                                <td>{{Str::limit($auftrag->format)}}</td>
                                <td>{{Str::limit($auftrag->gewicht)}} {{($auftrag->gewicht) ? 'g' : ''}}</td>
                                <td>{{Str::limit($auftrag->schlager)}}</td>
								<td>{{ number_format($auftrag->best_stueck, 0, ',', '.') }}</td>
								<td>{{Str::limit($auftrag->kundenbranche)}}</td>

								{{-- <td>{{Str::limit($auftrag->tat_bundeslaender, 10)}}</td>--}}
								<td style="max-width: 200px; white-space: nowrap; overflow: hidden;">
									<div class="row">
										<div class="col-12">
											@if($auftrag->deleted_at)
												<a class="btn btn-success btn-sm action-btn float-end me-1 text-white" data-action="restore" data-value="{{$auftrag->id}}">
													<i class="fa-solid fa-trash-can-arrow-up"></i>
												</a>
											@endif
											@if($auftrag->deleted_at == null)
												<a data-id="" class="btn btn-sm btn-danger me-1 float-end action-btn" data-action="storno" data-value="{{$auftrag->id}}">
													<i class="fas fa-trash"></i>
												</a>

												<a href="{{url("/backend/buss/planungskalender/edit/".$auftrag->id)}}" class="btn btn-sm btn-secondary me-1 float-end ">
													<i class="fas fa-gear"></i>
												</a>
												<a data-id="{{$auftrag->id}}" class="btn btn-sm btn-warning me-1 float-end action-btn" data-action="copy" data-value="{{$auftrag->id}}">
													<i class="fas fa-copy"></i>
												</a>
												<a class="btn btn-outline-dark btn-sm float-end me-1 action-btn  " data-action="view" data-value="{{$auftrag->id}}">
													<i class="fa-solid fa-magnifying-glass"></i>
												</a>
											@endif
										</div>
									</div>
								</td>
							</tr>
						@endforeach
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>





	<div class="modal fade" id="dataModal" tabindex="-1" aria-labelledby="dataModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content detail-content">

			</div>
		</div>
	</div>

	<div class="modal fade" id="stornoModal" tabindex="-1" aria-labelledby="stornoModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content  storno-content">

			</div>
		</div>
	</div>

	<div class="modal fade" id="restoreModal" tabindex="-1" aria-labelledby="restoreModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content  restore-content">

			</div>
		</div>
	</div>

	<div class="modal fade" id="copyModal" tabindex="-1" aria-labelledby="copyModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header bg-warning">
					<h5 class="modal-title" id="copyModalLabel">Reservierung kopieren</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<!-- Tabs -->
					<ul class="nav nav-tabs" id="copyModalTabs" role="tablist">
						<li class="nav-item" role="presentation">
							<button class="nav-link text-dark active" id="calendar-tab" data-bs-toggle="tab" data-bs-target="#calendar-tab-pane" type="button" role="tab" aria-controls="calendar-tab-pane" aria-selected="true">
								Reservierung kopieren
							</button>
						</li>
						<li class="nav-item" role="presentation">
							<button class="nav-link text-dark" id="manual-tab" data-bs-toggle="tab" data-bs-target="#manual-tab-pane" type="button" role="tab" aria-controls="manual-tab-pane" aria-selected="false">
								Serienreservierung
							</button>
						</li>
						<li class="nav-item" role="presentation">
							<button class="nav-link text-dark" id="uebertragung-tab" data-bs-toggle="tab" data-bs-target="#uebertragung-tab-pane" type="button" role="tab" aria-controls="uebertragung-tab-pane" aria-selected="false">
								Übertragung Krone/Kurier
							</button>
						</li>
					</ul>
					<div class="tab-content pt-3" id="copyModalTabContent">
						<div class="tab-pane fade show active" id="calendar-tab-pane" role="tabpanel" aria-labelledby="calendar-tab">
							<div class="row">
								<div class="col-md-8">
									<!-- Kalender Container -->
									<div id="calendar"></div>
								</div>
								<div class="col-md-4">
									<!-- Liste der ausgewählten Tage -->
									<div class="card">
										<div class="card-header">
											Ausgewählte Tage
										</div>
										<div class="card-body" style="max-height: 20rem!important; overflow-y: scroll;">
											<ul id="selectedDates" class="list-group">
												<!-- Hier werden die ausgewählten Tage eingefügt -->
											</ul>
										</div>
									</div>
								</div>
							</div>
							<input type="hidden" id="auftragId" value="">
							<hr>
							<button type="button" class="btn btn-warning float-end  " id="copyReservierungBtn">Kopieren</button>
							<button type="button" class="btn btn-secondary float-end me-1" data-bs-dismiss="modal">Abbrechen</button>
						</div>


						<!-- Tab 2: Manuelle Auswahl -->
						<div class="tab-pane fade" id="manual-tab-pane" role="tabpanel" aria-labelledby="manual-tab">
							<div class="row mb-3">
								<div class="col-md-6">
									<label for="date-from" class="form-label">Von Datum:</label>
									<input type="date" class="form-control" id="date-from">
								</div>
								<div class="col-md-6">
									<label for="date-to" class="form-label">Bis Datum:</label>
									<input type="date" class="form-control" id="date-to">
								</div>
							</div>

							<div class="row mb-3">
								<div class="col-12">
									<label class="form-label">Wochentage:</label>
									<div class="d-flex flex-wrap gap-3">
										<div class="form-check">
											<input class="form-check-input weekday-check" type="checkbox" value="1" id="monday-check">
											<label class="form-check-label" for="monday-check">Montag</label>
										</div>
										<div class="form-check">
											<input class="form-check-input weekday-check" type="checkbox" value="2" id="tuesday-check">
											<label class="form-check-label" for="tuesday-check">Dienstag</label>
										</div>
										<div class="form-check">
											<input class="form-check-input weekday-check" type="checkbox" value="3" id="wednesday-check">
											<label class="form-check-label" for="wednesday-check">Mittwoch</label>
										</div>
										<div class="form-check">
											<input class="form-check-input weekday-check" type="checkbox" value="4" id="thursday-check">
											<label class="form-check-label" for="thursday-check">Donnerstag</label>
										</div>
										<div class="form-check">
											<input class="form-check-input weekday-check" type="checkbox" value="5" id="friday-check">
											<label class="form-check-label" for="friday-check">Freitag</label>
										</div>
										<div class="form-check">
											<input class="form-check-input weekday-check" type="checkbox" value="6" id="saturday-check">
											<label class="form-check-label" for="saturday-check">Samstag</label>
										</div>
										<div class="form-check">
											<input class="form-check-input weekday-check" type="checkbox" value="0" id="sunday-check">
											<label class="form-check-label" for="sunday-check">Sonntag</label>
										</div>
									</div>
								</div>
							</div>

							<hr>
							<button type="button" class="btn btn-warning float-end" id="manualCopyBtn">Kopieren</button>
							<button type="button" class="btn btn-secondary float-end me-1" data-bs-dismiss="modal">Abbrechen</button>
						</div>

						<div class="tab-pane fade  " id="uebertragung-tab-pane" role="tabpanel" aria-labelledby="uebertragung-tab">

							<div class="row">

								<div class="col-12 text-center mb-4">
									<h5>Übertragung zwischen Systemen</h5>
									<p class="text-muted">Hier können Sie die Planung zum anderen System übertragen.</p>
								</div>

								<div class="col-12 d-flex justify-content-center">
									<button type="button" class="btn btn-danger btn-lg text-dark" id="transferToKurierBtn">
										<i class="fas fa-exchange-alt me-2"></i>Planung zum {{strtoupper(env('MEDIA') == 'krone' ? 'kurier' : 'krone')}}-System übertragen
                                    </button>
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="alert alert-info" role="alert">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Die Übertragung kopiert alle Daten des aktuellen Planungseintrags zum anderen System.
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>

@endsection

@extends('backend.content.index')

@section('dynamicCSS')
    <link rel="stylesheet" href="{{url("inc/DataTables/datatables.css")}}?v=3">
@endsection

@section('postscripts')
    <script src="{{ asset('inc/DataTables/datatables.js') }}"></script>
    <script src="{{ asset('inc/fullcalendar-6.1.15/dist/index.global.min.js') }}"></script>
    <script src="{{ asset('inc/initDatatable.js') }}"></script>
    <script src="{{ asset('inc/buss/planungskalender.js') }}"></script>

@endsection

@section('card-content')

    <div class="row mb-2">
        <div class="col-12">
            @if($auftrag->memo)
                <div class="alert alert-info mb-0 py-2 px-3 position-relative" role="alert" style="border-left: 5px solid #198754;">
                    <strong>Reservierungsmemo</strong>
                    <div class="mt-1" style="word-wrap: break-word; white-space: normal;">
                        {!! nl2br(e($auftrag->memo)) !!}
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-dark position-absolute"
                            style="right: 10px; top: 6px;"
                            id="editnotizBtn"
                            data-bs-toggle="modal"
                            data-bs-target="#notizModal">
                        <i class="fa-solid fa-comment me-1"></i>Reservierungsmemo bearbeiten
                    </button>

                </div>
            @else
                <button type="button" class="btn btn-sm btn-outline-info "

                        id="editnotizBtn"
                        data-bs-toggle="modal"
                        data-bs-target="#notizModal">
                    <i class="fa-solid fa-plus me-1"></i>Reservierungsmemo hinzufügen
                </button>
            @endif
        </div>
    </div>
    <div class="row">
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
    </div>
    <div class="row">
        <form action="{{route('backend.beilagenrechner.planungskalender.update', $auftrag->id)}}" method="POST" id="planungskalenderform">
            @csrf
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0 fw-bold">Allgemeine Daten</h5>
                            <span class="small">*Pflichtfelder</span>
                        </div>
                        <div class="card-body bg-light">
                            <div class="row">
                                <div class="col-12">
                                    <div class="row mb-3">
                                        <div class="col-12 col-md-4 ">
                                            <label for="objekt">*Objekt:</label>
                                            <select id="bran" class="form-control form-select" name="objekt">
                                                <option value="{{null}}">Objekt wählen</option>
                                                @foreach(config('backend.buss.options.Objekt_'.env('MEDIA')) as $key => $option)
                                                    <option value="{{$key}}" {{ ($selectedObjekt == $key) ? 'selected' : '' }} {{ old('objekt', $auftrag->objekt) == $key ? 'selected' : '' }}>{{$option}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-12 col-md-4">
                                            <label for="format">Format:</label>
                                            <input type="text" id="format" class="form-control" name="format" placeholder="Format eingeben" value="{{ old('format', $auftrag->format) }}">
                                        </div>
                                        <div class="col-4">
                                            <label for="stueck">Best. Stück:</label>
                                            <input type="number" id="stueck" name="beststueck" class="form-control" min="0" placeholder="Bitte die Anzahl eintragen" value="{{ old('beststueck', $auftrag->best_stueck) }}">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-4">
                                            <label for="erdatum">*Er Datum:</label>
                                            <input type="date" id="erdatum" name="erdatum" class="form-control" placeholder="" value="{{ ($selectedDate) ?? old('erdatum', $auftrag->er_datum ? \Carbon\Carbon::parse($auftrag->er_datum)->format('Y-m-d') : '') }}">
                                        </div>
                                        <div class="col-12 col-md-4">
                                            <label for="gewicht">Gewicht:</label>
                                            <input type="text" id="gewicht" class="form-control" name="gewicht" placeholder="Gewicht eingeben" value="{{ old('gewicht', $auftrag->gewicht) }}">
                                        </div>
                                        <div class="col-12 col-md-4 ">
                                            <label for="verrechner">Verrechner:</label>
                                            <input type="text" id="verrechner" class="form-control" name="verrechner" placeholder="Verrechner eingeben" value="{{ old('verrechner', $auftrag->verrechner) }}">
                                        </div>
                                    </div>
                                    <div class="row">

                                        <div class="col-12 col-md-4">
                                            <label for="inserent">*Inserent:</label>
                                            <select id="inserent" class="form-control form-select" name="inserent">
                                                <option value="{{null}}">Kunden wählen</option>
                                                @foreach($kunden as $key => $kunde)
                                                    <option value="{{$kunde->buss_gp_nummer.'|'.$kunde->name}}"
                                                        {{ explode('|', old('inserent', $auftrag->inserent))[0] == $kunde->buss_gp_nummer ? 'selected' : '' }}>
                                                        {{$kunde->name}}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-12 col-md-4">
                                            <label for="schlager">Schlager:</label>
                                            <input type="text" id="schlager" class="form-control" name="schlager" placeholder="Schlager eingeben" value="{{ old('schlager', $auftrag->schlager) }}">
                                        </div>
                                        <div class="col-4">
                                            <label for="bran">Kundenbranche:</label>
                                            <select id="bran" class="form-control form-select" name="kundenbranche">
                                                <option value="option1" {{ old('kundenbranche', $auftrag->kundenbranche) == 'option1' ? 'selected' : '' }}>Option 1</option>
                                                <option value="option2" {{ old('kundenbranche', $auftrag->kundenbranche) == 'option2' ? 'selected' : '' }}>Option 2</option>
                                                <option value="option3" {{ old('kundenbranche', $auftrag->kundenbranche) == 'option3' ? 'selected' : '' }}>Option 3</option>
                                            </select>
                                        </div>
                                    </div>

                                </div>

                            </div>


                        </div>


                    </div>
                </div>
                <div class="col-md-7">
                    <div class="card h-100 bg-light ">
                        <div class="card-header bg-light">
                            <h5 class="mb-0 fw-bold">Beilagenart & Reservierte Bundesländer</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="beart" id="fremdbeilage" value="fremdbeilage" {{ old('beart', $auftrag->beart) == 'fremdbeilage' ||  (is_null(old('beart')) && is_null($auftrag->beart))  ? 'checked' : '' }}>
                                        <label class="form-check-label" for="fremdbeilage">
                                            Fremdbeilage
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="beart" id="eigenbeilage" value="eigenbeilage" {{ old('beart', $auftrag->beart) == 'eigenbeilage' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="eigenbeilage">
                                            Eigenbeilage
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-4">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="wi" value="wi" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('wi', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'wi') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="wi">Wien (WI)</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="no" value="no" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('no', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'no') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="no">Niederösterreich (NO)</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="bg" value="bg" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('bg', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'bg') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="bg">Burgenland (BG)</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="st" value="st" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('st', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'st') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="st">Steiermark (ST)</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="kt" value="kt" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('kt', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'kt') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="kt">Kärnten (KT)</label>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="oo" value="oo" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('oo', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'oo') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="oo">Oberösterreich (OO)</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="sb" value="sb" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('sb', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'sb') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="sb">Salzburg (SB)</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="ti" value="ti" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('ti', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'ti') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="ti">Tirol (TI)</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="bundeslaender[]" id="vo" value="vo" {{ is_array(old('bundeslaender'))
                                                    ? (in_array('vo', old('bundeslaender')) ? 'checked' : '')
                                                    : (strpos($auftrag->bundeslaender, 'vo') !== false ? 'checked' : '') }}>
                                                <label class="form-check-label" for="vo">Vorarlberg (VO)</label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-grid gap-2 mt-3">
                                                <button type="button" class="btn btn-sm btn-outline-primary" id="select-all-bdl">
                                                    <i class="fas fa-check-square me-1"></i> Alle auswählen
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all-bdl">
                                                    <i class="fas fa-square me-1"></i> Alle abwählen
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-5">
                    <div class="card bg-light ">
                        <div class="card-header bg-light">
                            <h5 class="mb-0 fw-bold">Tatsächlich gebuchte Bundesländer</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <small>
                                            <i class="fas fa-info-circle me-1"></i>
                                            Solange keine Buchung zu dieser Reservierung vorliegt, werden die ursprünglich geplanten (gebuchten) Bundesländer angezeigt. Sobald eine Buchung erfolgt ist, werden die tatsächlich gebuchten Bundesländer dargestellt
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    @if(isset($erfassung) && $erfassung->tat_bundeslaender)
                                        <h6 class="text-muted fw-bold text-uppercase">Tatsächlich gebucht:</h6>
                                        @foreach(json_decode($erfassung->tat_bundeslaender) as $land)
                                            <span class="badge bg-primary text-white me-1 fs-6 rounded-pill text-uppercase">
                                              {{ $land }}
                                            </span>
                                        @endforeach
                                        @if($auftrag->bundeslaender)
                                            <h6 class="text-muted fw-bold text-uppercase mt-3">Reserviert:</h6>
                                            @foreach(json_decode($auftrag->bundeslaender) as $land)
                                                <span class="badge bg-warning text-dark me-1 fs-6 rounded-pill text-uppercase">
                                              {{ $land }}
                                            </span>
                                            @endforeach

                                        @endif
                                    @elseif($auftrag->bundeslaender != null)
                                        <h6 class="text-muted fw-bold text-uppercase mt-3">Reserviert:</h6>
                                        @foreach(json_decode($auftrag->bundeslaender) as $land)
                                            <span class="badge bg-warning text-dark me-1 fs-6 rounded-pill text-uppercase">
                                              {{ $land }}
                                            </span>
                                        @endforeach
                                    @endif
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="col-12 my-3">
                    <button type="submit" class="btn btn-success text-white float-end  ">
                        <i class="fa-solid fa-save me-1"></i>Speichern
                    </button>

                    <a class="btn btn-danger float-end me-2" href="{{route('backend.beilagenrechner.planungskalender')}}">
                        <i class="fa-solid fa-xmark me-1"></i>Abbrechen
                    </a>
                </div>

            </div>


        </form>
    </div>
    <div class="row">
        <div class="col-12 mb-3">
            <h1>Changelog</h1>
        </div>
        <div class="col-12">
            @foreach()

            @endforeach
            <div class="card">
                <div class="card-body">

                </div>
            </div>
        </div>

    </div>




    <!-- Modal für Notizen -->
    <div class="modal fade" id="notizModal" tabindex="-1" aria-labelledby="notizModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="notizModalLabel">Notizen</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="notizForm" action="{{ route('backend.beilagenrechner.planungskalender.save-notiz', $auftrag->id) }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <textarea class="form-control" id="reservierungsNotiz" name="notiz" rows="10">{!!  $auftrag->memo ?? '' !!}</textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen</button>
                    <button type="button" class="btn btn-info text-white" id="saveNotizBtn">Speichern</button>
                </div>
            </div>
        </div>
    </div>

@endsection

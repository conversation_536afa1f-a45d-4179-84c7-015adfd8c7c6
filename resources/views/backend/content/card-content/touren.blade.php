@extends('backend.content.index')

@section('dynamicCSS')
	<link rel="stylesheet" href="{{url("inc/DataTables/datatables.css")}}?v=3">
@endsection

@section('postscripts')
	<script src="{{ asset('inc/DataTables/datatables.js') }}"></script>
	<script src="{{ asset('inc/initDatatable.js') }}"></script>
	<script src="{{ asset('inc/plzverwaltung.js') }}"></script>

@endsection

@section('card-content')
	<div class="row">
		<div class="col-12">
			<a class="btn btn-success  float-end text-white " href="{{route('create.touren')}}" >Neue Tour hinzufügen</a>
			<table id="tarifeTable" class="table table-bordered table-striped">
				<thead>
				<tr>
					<th>Tour</th>
					<th>Sammeltour</th>
					<th>Bezeichnung</th>
					<th>Bundesland</th>
					<th></th>
				</tr>
				</thead>
				<tbody>
				@foreach($touren as $tour)
					<tr id="tr_{{$tour->tour}}">
						<td>{{$tour->tour}}</td>
						<td>{{$tour->sammeltour}}</td>
						<td>{{$tour->bezeichnung1}}</td>
						<td>{{$tour->bundesland}}</td>
						<td style="max-width: 200px; white-space: nowrap; overflow: hidden;">
							<div class="row">
								<div class="col-12">
									<a data-id="{{$tour->id}}" class="btn btn-sm btn-danger me-1 float-end delete-plz-btn">
										<i class="fas fa-trash"></i>
									</a>
                                    <a href="{{ route('edit.touren', ['id' => $tour->id]) }}" class="btn btn-sm btn-secondary me-1 float-end">
                                        <i class="fas fa-gear"></i>
                                    </a>

                                    <a href="#" data-id="{{$tour->id}}" class="btn btn-sm btn-warning me-1 float-end copy-plz-btn">
                                        <i class="fas fa-copy"></i>
                                    </a>
								</div>
							</div>
						</td>
					</tr>
				@endforeach
				</tbody>
			</table>
		</div>
	</div>


@endsection

<?php

namespace App\Models\Buss;


use App\Models\RechnerConfigTarif;
use App\Models\RechnerConfigTarifzone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Changelog extends Model
{
    use HasFactory, SoftDeletes;

    protected $connection = 'mysql';
    protected $guarded = ['id'];
    protected $table = "buss_changelog";

    /**
     * Die Attribute, die als Datum behandelt werden sollen.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
    ];

    /**
     * Die Attribute, die Typumwandlungen haben sollen.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    public function reservierung()
    {
        return $this->belongsTo(Planung::class, 'reservierungs_id');
    }




}

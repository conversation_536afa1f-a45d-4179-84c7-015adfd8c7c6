<?php

namespace App\Models\Buss\Kurier;


use App\Models\Buss\Auftrag;
use App\Models\RechnerConfigTarif;
use App\Models\RechnerConfigTarifzone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Planung extends Model
{
    use HasFactory, SoftDeletes;

    protected $connection = 'mysql2';
    protected $guarded = ['id'];
    protected $table = "buss_planungseintrag";

    /**
     * Die Attribute, die als Datum behandelt werden sollen.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Die Attribute, die Typumwandlungen haben sollen.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    public function auftrag()
    {
        // Die Planung "gehört zu" einem Auftrag, Fremdschlüssel ist `auftrags_id` in Planung
        return $this->belongsTo(Auftrag::class, 'auftrags_id');
    }




}

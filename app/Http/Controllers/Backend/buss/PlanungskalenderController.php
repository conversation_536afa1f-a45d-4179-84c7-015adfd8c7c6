<?php

    namespace App\Http\Controllers\Backend\buss;


    use App\Facades\AuthUser;
    use App\Http\Controllers\Old\Controller;
    use App\Mail\Reservierung;
    use App\Models\Buss\Auftraege;
    use App\Models\Buss\Auftrag;
    use App\Models\Buss\Changelog;
    use App\Models\Buss\Kunden;
    use App\Models\Buss\Kurier\Planung as KurierPlanung;
    use App\Models\Buss\Planung as Planung;

    use App\Models\Buss\Tagesnotiz;
    use App\Models\User;
    use Carbon\Carbon;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\Mail;


    class PlanungskalenderController extends Controller{


        public function index($storno = null){


            $data['menuItems'] = collect(config('backend.menuitems')());

            $query = Planung::query();

            if($storno === 'storno'){
                $query->withTrashed();
            }else{
                $query->whereNull('deleted_at');
            }


            $selectedDate = request('datum')
                            ?? session('planungskalender_datum')
                               ?? now()->format('Y-m-d');


            session(['planungskalender_datum' => $selectedDate]);


            if(request()->has('datum')){
                $query->whereDate('er_datum', $selectedDate);
            }


            if(request('beilagenart') != 'all'){
                $selectedBeart = request('beilagenart') ? request('beilagenart') : '';

                if(request()->has('beilagenart') && request('beilagenart')){
                    $art = request('beilagenart');
                    $query->where('beart', $art);

                    $data['selectedBeart'] = $selectedBeart;
                }
            }


            /*    if(request()->has('objekt') && request('objekt') != 'all'){

                    $selectedObjekt = request('objekt') ? request('objekt') : '';

                    if(request('objekt')){
                        $objekt = request('objekt');
                        $objekt = request('objekt');
                        $query->where('objekt', $objekt);

                        $data['selectedObjekt'] = $selectedObjekt;
                    }
                }*/

            // 1. Standardwert setzen je nach MEDIA
            $defaultObjekt = match (env('MEDIA')) {
                'krone' => 'krone',
                'kurier' => 'kurier',
                default => 'all', // Fallback
            };

            // 2. Objektwert ermitteln: zuerst aus Request, dann Session, sonst Default
            $selectedObjekt = request('objekt')
                              ?? session('planungskalender_objekt')
                                 ?? $defaultObjekt;

            // 3. In Session speichern, falls neu übergeben
            if(request()->has('objekt')){
                session(['planungskalender_objekt' => $selectedObjekt]);
            }

            // 4. Query anwenden, falls Objekt nicht 'all' ist
            if($selectedObjekt !== 'all'){
                $query->where('objekt', $selectedObjekt);
            }

            // 5. Objekt für die View bereitstellen (wenn du es brauchst)
            $data['selectedObjekt'] = $selectedObjekt;


            $data['tagesnotiz'] = Tagesnotiz::where('tag', $selectedDate)->first();
            $data['selectedDate'] = $selectedDate;

            // Feiertag prüfen
            $jahr = substr($selectedDate, 0, 4);
            $feiertage = config("backend.buss.feiertage.$jahr", []);
            $data['feiertagName'] = $feiertage[ $selectedDate ] ?? null;


            $query->orderBy('er_datum', 'desc');

            $data['auftraege'] = $query->where('er_datum', $selectedDate)->get();

            return view('backend.buss.card-content.planungskalender', $data);
        }


        public
        function edit(Planung $id){

            dd($id);
            $data['menuItems'] = collect(config('backend.menuitems')());
            $data['auftrag'] = $id;
            $data['tagesnotiz'] = Tagesnotiz::where('tag', $id->er_datum)->first();
            $data['kunden'] = Kunden::all();
            $data['erfassung'] = $id->auftrag;
            $selectedDate = request('datum')
                            ?? session('planungskalender_datum')
                               ?? now()->format('Y-m-d');
            $selecteObjekt = request('objekt')
                             ?? session('planungskalender_objekt')
                                ?? now()->format('Y-m-d');

            session(['planungskalender_datum' => $selectedDate]);
            session(['planungskalender_objekt' => $selecteObjekt]);

            // Gib's an die View weiter (optional, aber sauber):
            $data['selectedDate'] = $selectedDate;
            $data['selectedObjekt'] = $selecteObjekt;


            return view('backend.buss.edit.planungskalender', $data);
        }


        public
        function update(Request $request, Planung $auftrag){
            $validated = $request->validate([
                'memo' => 'nullable|string|max:255',
                'erdatum' => 'required|date|after_or_equal:1900-01-01',
                'beststueck' => 'nullable|numeric|min:0',
                'kundenbranche' => 'nullable|string|max:255',
                'inserent' => 'required|string|min:1',
                'verrechner' => 'nullable|string|max:255',
                'schlager' => 'nullable|string|max:255',
                'format' => 'nullable|string',
                'gewicht' => 'nullable|string|min:0',
                'beart' => 'required|string',
                'beart.*' => 'string|max:255',
                'objekt' => 'required|string|max:255',
            ], [
                'erdatum.required' => 'Bitte das Erdatum eingeben.',
                'erdatum.date' => 'Das Erdatum muss ein gültiges Datum sein.',
                'erdatum.after_or_equal' => 'Das Erdatum darf nicht vor dem Jahr 1900 liegen.',
                'beststueck.numeric' => 'Beststueck muss eine Zahl sein.',
                'beststueck.min' => 'Beststueck darf nicht negativ sein.',
                'inserent.required' => 'Der Inserent ist erforderlich.',
                'inserent.string' => 'Der Inserent muss eine Zeichenkette sein.',
                'schlager.*.string' => 'Jedes Schlagwort muss ein Text sein.',
                'format.in' => 'Das Format muss eines der folgenden sein: A4, A3, A5, B5, Letter, Legal, Tabloid.',
                'gewicht.string' => 'Das Gewicht muss ein Text sein.',
                'beart.string' => 'Beart muss ein Text sein.',
                'objekt.required' => 'Bitte ein Objekt angeben.',
            ]);

            // Store original data before update
            $originalData = $auftrag->toArray();

            $updateData = [
                'er_datum'      => $validated['erdatum'] ?? null,
                'best_stueck'   => $validated['beststueck'] ?? null,
                'kundenbranche' => $validated['kundenbranche'] ?? null,
                'inserent'      => $validated['inserent'] ? (int) explode('|', $validated['inserent'])[0] : null,
                'kundenname'    => $validated['inserent'] ? explode('|', $validated['inserent'])[1] : null,
                'verrechner'    => $validated['verrechner'] ?? null,
                'schlager'      => $validated['schlager'] ?? null,
                'format'        => $validated['format'] ?? null,
                'gewicht'       => $validated['gewicht'] ?? null,
                'beart'         => $validated['beart'] ?? null,
                'objekt'        => $validated['objekt'] ?? null,
            ];

            $bundeslaender = [];

            if($request->has('bundeslaender')){
                $beartOrt = $request->input('bundeslaender');
                if(is_array($beartOrt)){
                    $bundeslaender = $beartOrt;
                }else{
                    $bundeslaender = ['all'];
                }
            }

            $updateData['bundeslaender'] = !empty($bundeslaender) ? json_encode($bundeslaender) : null;

            // Update the record
            $auftrag->update($updateData);
            $auftrag->save();

            // Get updated data
            $updatedData = $auftrag->fresh()->toArray();

            // Determine what changed
            $changes = [];
            foreach ($updateData as $key => $value) {
                if (isset($originalData[$key]) && $originalData[$key] !== $updatedData[$key]) {
                    $changes[$key] = [
                        'old' => $originalData[$key],
                        'new' => $updatedData[$key]
                    ];
                }
            }

            // Create changelog entry
            Changelog::create([
                'reservierungs_id' => $auftrag->id,
                'reservierungs_data' => json_encode($originalData),
                'changed_data' =>  json_encode($changes),
                'updated_by' => AuthUser::get()->id,
                'updated_at'    => now(),
                'created_at'    => now(),
            ]);

            return redirect()
                ->route('backend.beilagenrechner.planungskalender')
                ->with('success', 'Auftrag wurde erfolgreich aktualisiert.');
        }


        public
        function create($objekt = null){
            $created = Planung::create([

                                           'er_datum' => now()->format('Y-m-d'),
                                           'objekt'   => $objekt ?? env('MEDIA'),
                                       ]);

            $created->save();

            return redirect(route('backend.beilagenrechner.planungskalender.edit', [$created->id]));
        }


        public
        function detail(Planung $auftrag){


            $data['planung'] = $auftrag;
            if($auftrag->auftrag()){
                $data['auftrag'] = $auftrag->auftrag;
            }


            $view = view('backend.buss.partials.detail-modal-content', $data)->render();

            return response()->json($view);
        }


        public
        function storno($id){

            $auftrag = [
                'id' => $id,
            ];

            $view = view('backend.buss.partials.storno-modal-content', [
                'auftrag' => $auftrag
            ])->render();

            return response()->json([
                                        'success' => true,
                                        'message' => 'Auftrag wurde erfolgreich storniert.',
                                        'html'    => $view
                                    ]);
        }


        public
        function storno_confirmed(Planung $id){

            $getAuftrag = Auftrag::where('reservierungs_id', $id->id)->first();
            if($getAuftrag){
                $getAuftrag->reservierungs_id = null;
                $getAuftrag->save();
                $id->auftrags_id = null;
                $id->save();
            }

            $id->delete();
            $this->sendMail($id, 'storno');

            return redirect()->back()->with('success', 'Auftrag wurde erfolgreich storniert.');
        }


        public
        function restore($id){
            $auftrag = [
                'id' => $id,
            ];

            $view = view('backend.buss.partials.restore-modal-content', [
                'auftrag' => $auftrag
            ])->render();

            return response()->json([
                                        'success' => true,
                                        'message' => 'Auftrag wurde erfolgreich storniert.',
                                        'html'    => $view
                                    ]);
        }


        public
        function restore_confirmed(Planung $id){

            $id->restore();
            $this->sendMail($id, 'wiederherstellung');
            return redirect()->back()->with('success', 'Auftrag wurde erfolgreich wiederhergestellt.');
        }


        public
        function saveTagesnotiz(Request $request){

            if($request->notiz == ''){
                // Wenn die Notiz leer ist, lösche den Datensatz
                Tagesnotiz::where('tag', $request->tag)->delete();
                return redirect()->back()->with('success', 'Tagesnotiz wurde gelöscht.');
            };

            Tagesnotiz::updateOrCreate(
                ['tag' => $request->tag],
                ['notiz' => $request->notiz]
            );

            return redirect()->back();
        }


        public
        function saveNotiz(Request $request, Planung $id){

            $id->memo = $request->notiz;
            $id->save();

            return redirect()->back();
        }


        public
        function copyToDates(Request $request){

            $originalAuftrag = Planung::findOrFail($request->auftrag_id);
            $copiedCount = 0;

            foreach($request->dates as $date){
                $newAuftrag = $originalAuftrag->replicate();

                $newAuftrag->er_datum = $date;


                $newAuftrag->save();

                $copiedCount++;
            }

            return response()->json([
                                        'success' => true,
                                        'message' => "Reservierung wurde erfolgreich für {$copiedCount} Tage kopiert.",
                                        'count'   => $copiedCount
                                    ]);
        }


        public function suche(Request $request){
            $query = $request->get('q', '');

            $planungen = Planung::when($query, function($q) use ($query){
                $q->where('kundenname', 'like', "%$query%");
            })
                                ->get();

            return response()->json($planungen);
        }


        public function zuweisungDelete(Auftrag $id){
            $getPlanung = Planung::where('auftrags_id', $id->id)->first();
            $getPlanung->auftrags_id = null;
            $getPlanung->save();
            $id->reservierungs_id = null;
            $id->save();

            return redirect()->back()->with('success', 'Die Zuweisung wurde erfolgreich gelöscht.');
        }


        public function copyToWeekdays(Request $request){


            $originalAuftrag = Planung::findOrFail($request->auftrag_id);
            $dateFrom = Carbon::parse($request->date_from);
            $dateTo = Carbon::parse($request->date_to);
            $weekdays = $request->weekdays;
            $copiedCount = 0;

            // Durchlaufe jeden Tag im Datumsbereich
            $currentDate = $dateFrom->copy();
            while($currentDate->lte($dateTo)){
                // Prüfe, ob der aktuelle Tag in den ausgewählten Wochentagen ist
                if(in_array((string) $currentDate->dayOfWeek, $weekdays)){
                    // Kopiere den Auftrag mit neuem Datum
                    $newAuftrag = $originalAuftrag->replicate();
                    $newAuftrag->er_datum = $currentDate->format('Y-m-d');
                    $newAuftrag->save();
                    $copiedCount++;
                }

                $currentDate->addDay();
            }

            return response()->json([
                                        'success' => true,
                                        'message' => "Reservierung wurde erfolgreich für {$copiedCount} Tage kopiert.",
                                        'count'   => $copiedCount
                                    ]);
        }


        public function transferToKurier(Request $request){
            // Finde die Originalplanung
            $originalPlanung = Planung::findOrFail($request->auftrag_id);

            // Erstelle eine neue Planung im Kurier-System
            $kurierPlanung = new KurierPlanung();

            // Übertrage alle relevanten Attribute
            $kurierPlanung->er_datum = $originalPlanung->er_datum;
            $kurierPlanung->best_stueck = $originalPlanung->best_stueck;
            $kurierPlanung->kundenbranche = $originalPlanung->kundenbranche;
            $kurierPlanung->inserent = $originalPlanung->inserent;
            $kurierPlanung->kundenname = $originalPlanung->kundenname;
            $kurierPlanung->verrechner = $originalPlanung->verrechner;
            $kurierPlanung->schlager = $originalPlanung->schlager;
            $kurierPlanung->format = $originalPlanung->format;
            $kurierPlanung->gewicht = $originalPlanung->gewicht;
            $kurierPlanung->beart = $originalPlanung->beart;
            $kurierPlanung->objekt = $originalPlanung->objekt;
            $kurierPlanung->bundeslaender = $originalPlanung->bundeslaender;
            $kurierPlanung->tat_bundeslaender = $originalPlanung->tat_bundeslaender;
            $kurierPlanung->memo = $originalPlanung->memo;

            // Speichere die neue Planung
            $kurierPlanung->save();

            return response()->json([
                                        'success'    => true,
                                        'message'    => 'Die Planung wurde erfolgreich zum Kurier-System übertragen.',
                                        'planung_id' => $kurierPlanung->id
                                    ]);
        }


        public function sendMail($id, $typ){

            if(env('APP_ENV') == 'production'){
                $ausnahmeuser = [
                    "sschick",
                    "faprea",
                    "dargiola",
                    "lblaha",
                    "vnikolic"
                ];

                $getAllUsers = User::all();

                foreach($getAllUsers as $user){
                    if(! in_array($user->username, $ausnahmeuser) || AuthUser::get()->username == $user->username){
                        Mail::to($user->email)->send(new Reservierung($id, $typ));
                    }
                }
            }else{
                Mail::to('<EMAIL>')->send(new Reservierung($id, $typ));
            }


        }
    }

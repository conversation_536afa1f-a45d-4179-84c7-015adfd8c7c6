<?php

    namespace App\Http\Controllers\Backend;

    use App\Http\Controllers\Old\Controller;
    use App\Models\RechnerHzTouren;
    use App\Models\RechnerTarif;
    use App\Models\RechnerTouren;
    use Illuminate\Http\Request;


    class TourenController extends Controller{


        public function create(){


            $data['tour'] = RechnerTouren::create([
                                                         "sammeltour"    => 0,
                                                         "hidden"        => 0,
                                                         "nur_so"        => 0,
                                                         "nicht_so"      => 0,
                                                         "rank"          => 0,
                                                         "rank1"         => 0,
                                                         "auswahlbutton" => 0,
                                                     ]);


            $data['tour']->save();
            $data['tour']->refresh();



            return redirect()->route('edit.touren', $data['tour']->id);
        }


        public function edit(RechnerTouren $id){



            $data['menuItems'] = collect(config('backend.menuitems')());
            $data['tour'] = $tour;
            return view('backend.content.edit.touren', $data);
        }


        public function update(Request $request, RechnerTouren $tour){


            $validated = $request->validate([
                                                'hidden' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 'nur_so' => 'nullable|string|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 'nicht_so' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   'rank' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   'rank1' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   'auswahlbutton' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 'tour' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 'bezeichnung' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      'bezeichnung1' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     'bundesland' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     'ort' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     'volltext' => 'required|string',
                                                'touren' => 'required|string',
                                            ], [
                                                'hidden.integer' => 'Das Feld "hidden" muss eine Zahl sein.',
                                                'hidden.min'     => 'Der Wert für "hidden" muss mindestens 0 sein.',
                                                'hidden.max'     => 'Der Wert für "hidden" darf maximal 1 sein.',

                                                'nur_so.string' => 'Das Feld "nur_so" muss ein Text sein.',
                                                'nur_so.min'    => 'Der Wert für "nur_so" muss mindestens 0 sein.',
                                                'nur_so.max'    => 'Der Wert für "nur_so" darf maximal 1 sein.',

                                                'nicht_so.integer' => 'Das Feld "nicht_so" muss eine Zahl sein.',
                                                'nicht_so.min'     => 'Der Wert für "nicht_so" muss mindestens 0 sein.',
                                                'nicht_so.max'     => 'Der Wert für "nicht_so" darf maximal 1 sein.',

                                                'rank.integer' => 'Das Feld "rank" muss eine Zahl sein.',
                                                'rank.min'     => 'Der Wert für "rank" muss mindestens 0 sein.',
                                                'rank.max'     => 'Der Wert für "rank" darf maximal 1 sein.',

                                                'rank1.integer' => 'Das Feld "rank1" muss eine Zahl sein.',
                                                'rank1.min'     => 'Der Wert für "rank1" muss mindestens 0 sein.',
                                                'rank1.max'     => 'Der Wert für "rank1" darf maximal 1 sein.',

                                                'auswahlbutton.integer' => 'Das Feld "auswahlbutton" muss eine Zahl sein.',
                                                'auswahlbutton.min'     => 'Der Wert für "auswahlbutton" muss mindestens 0 sein.',
                                                'auswahlbutton.max'     => 'Der Wert für "auswahlbutton" darf maximal 1 sein.',

                                                'tour.required' => 'Das Feld "Tour" ist erforderlich.',
                                                'tour.string'   => 'Das Feld "Tour" muss ein Text sein.',

                                                'bezeichnung.required' => 'Das Feld "Bezeichnung" ist erforderlich.',
                                                'bezeichnung.string'   => 'Das Feld "Bezeichnung" muss ein Text sein.',

                                                'bezeichnung1.required' => 'Das Feld "Bezeichnung1" ist erforderlich.',
                                                'bezeichnung1.string'   => 'Das Feld "Bezeichnung1" muss ein Text sein.',

                                                'bundesland.required' => 'Das Feld "Bundesland" ist erforderlich.',
                                                'bundesland.string'   => 'Das Feld "Bundesland" muss ein Text sein.',

                                                'ort.required' => 'Das Feld "Ort" ist erforderlich.',
                                                'ort.string'   => 'Das Feld "Ort" muss ein Text sein.',

                                                'volltext.required' => 'Das Feld "Volltext" ist erforderlich.',
                                                'volltext.string'   => 'Das Feld "Volltext" muss ein Text sein.',

                                                'touren.required' => 'Das Feld "Touren" ist erforderlich.',
                                                'touren.string'   => 'Das Feld "Touren" muss ein Text sein.',
                                            ]);

            if(! $validated){
                return redirect()->back()->withInput()->withErrors($validated);
            }


            $tour->update(
                [
                    "hidden"        => $request->hidden,
                    "nur_so"        => $request->nur_so,
                    "nicht_so"      => $request->nicht_so,
                    "rank"          => $request->rank,
                    "rank1"         => $request->rank1,
                    "auswahlbutton" => $request->auswahlbutton,
                    "tour"          => $request->tour,
                    "bezeichnung"   => $request->bezeichnung,
                    "bezeichnung1"  => $request->bezeichnung1,
                    "bundesland"    => $request->bundesland,
                    "ort"           => $request->ort,
                    "volltext"      => $request->volltext,

                ]
            );

            $tarife = $request->tarife;

            foreach($tarife as $key => $value){

                RechnerTarif::where('id', $key)->update([
                                                            "jahr" => $value['jahr'],
                                                            "modo" => $value['modo'],
                                                            "fr"   => $value['fr'],
                                                            "sa"   => $value['sa'],
                                                            "so"   => $value['so'],
                                                        ]);
            }

            return redirect()->back();
        }


        public function addRow(Request $request){


            $tarif = new RechnerTarif();
            $tarif->tour = $request->tour_id;
            $tarif->jahr = now()->year;
            $tarif->modo = 0;
            $tarif->fr = 0;
            $tarif->sa = 0;
            $tarif->so = 0;
            $tarif->save();

            return response()->json(['id' => $tarif->id]);
        }


        public function removeRow(Request $request){

            $tarif = RechnerTarif::where('id', $request->id)->delete();


            return response()->json(['id' => $tarif]);
        }



        public function createHz(){
            $data['tour'] = RechnerHzTouren::create([
                                                      "sammeltour"    => 0,
                                                      "hidden"        => 0,
                                                      "nur_so"        => 0,
                                                      "nicht_so"      => 0,
                                                      "rank"          => 0,
                                                      "rank1"         => 0,
                                                      "auswahlbutton" => 0,
                                                  ]);

            $data['tour']->save();
            return redirect()->route('edit.touren_hz', $data['tour']->id);
        }

        public function editHz(RechnerHzTouren $tour){
            $data['menuItems'] = collect(config('backend.menuitems')());
            $data['tour'] = $tour;
            return view('backend.content.edit.touren_hz', $data);
        }


        public function updateHz(Request $request, RechnerHzTouren $tour){


            $validated = $request->validate([
                                                'hidden' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 'nur_so' => 'nullable|string|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 'nicht_so' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   'rank' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   'rank1' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   'auswahlbutton' => 'nullable|integer|min:0| max:1',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 'tour' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 'bezeichnung' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      'bezeichnung1' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     'bundesland' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     'ort' => 'required|string',
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     'volltext' => 'required|string',
                                                'touren' => 'required|string',
                                            ], [
                                                'hidden.integer' => 'Das Feld "hidden" muss eine Zahl sein.',
                                                'hidden.min'     => 'Der Wert für "hidden" muss mindestens 0 sein.',
                                                'hidden.max'     => 'Der Wert für "hidden" darf maximal 1 sein.',

                                                'nur_so.string' => 'Das Feld "nur_so" muss ein Text sein.',
                                                'nur_so.min'    => 'Der Wert für "nur_so" muss mindestens 0 sein.',
                                                'nur_so.max'    => 'Der Wert für "nur_so" darf maximal 1 sein.',

                                                'nicht_so.integer' => 'Das Feld "nicht_so" muss eine Zahl sein.',
                                                'nicht_so.min'     => 'Der Wert für "nicht_so" muss mindestens 0 sein.',
                                                'nicht_so.max'     => 'Der Wert für "nicht_so" darf maximal 1 sein.',

                                                'rank.integer' => 'Das Feld "rank" muss eine Zahl sein.',
                                                'rank.min'     => 'Der Wert für "rank" muss mindestens 0 sein.',
                                                'rank.max'     => 'Der Wert für "rank" darf maximal 1 sein.',

                                                'rank1.integer' => 'Das Feld "rank1" muss eine Zahl sein.',
                                                'rank1.min'     => 'Der Wert für "rank1" muss mindestens 0 sein.',
                                                'rank1.max'     => 'Der Wert für "rank1" darf maximal 1 sein.',

                                                'auswahlbutton.integer' => 'Das Feld "auswahlbutton" muss eine Zahl sein.',
                                                'auswahlbutton.min'     => 'Der Wert für "auswahlbutton" muss mindestens 0 sein.',
                                                'auswahlbutton.max'     => 'Der Wert für "auswahlbutton" darf maximal 1 sein.',

                                                'tour.required' => 'Das Feld "Tour" ist erforderlich.',
                                                'tour.string'   => 'Das Feld "Tour" muss ein Text sein.',

                                                'bezeichnung.required' => 'Das Feld "Bezeichnung" ist erforderlich.',
                                                'bezeichnung.string'   => 'Das Feld "Bezeichnung" muss ein Text sein.',

                                                'bezeichnung1.required' => 'Das Feld "Bezeichnung1" ist erforderlich.',
                                                'bezeichnung1.string'   => 'Das Feld "Bezeichnung1" muss ein Text sein.',

                                                'bundesland.required' => 'Das Feld "Bundesland" ist erforderlich.',
                                                'bundesland.string'   => 'Das Feld "Bundesland" muss ein Text sein.',

                                                'ort.required' => 'Das Feld "Ort" ist erforderlich.',
                                                'ort.string'   => 'Das Feld "Ort" muss ein Text sein.',

                                                'volltext.required' => 'Das Feld "Volltext" ist erforderlich.',
                                                'volltext.string'   => 'Das Feld "Volltext" muss ein Text sein.',

                                                'touren.required' => 'Das Feld "Touren" ist erforderlich.',
                                                'touren.string'   => 'Das Feld "Touren" muss ein Text sein.',
                                            ]);

            if(! $validated){
                return redirect()->back()->withInput()->withErrors($validated);
            }


            $tour->update(
                [
                    "hidden"        => $request->hidden,
                    "nur_so"        => $request->nur_so,
                    "nicht_so"      => $request->nicht_so,
                    "rank"          => $request->rank,
                    "rank1"         => $request->rank1,
                    "auswahlbutton" => $request->auswahlbutton,
                    "tour"          => $request->tour,
                    "bezeichnung"   => $request->bezeichnung,
                    "bezeichnung1"  => $request->bezeichnung1,
                    "bundesland"    => $request->bundesland,
                    "ort"           => $request->ort,
                    "volltext"      => $request->volltext,

                ]
            );

            $tarife = $request->tarife;

            foreach($tarife as $key => $value){

                RechnerTarif::where('id', $key)->update([
                                                            "jahr" => $value['jahr'],
                                                            "modo" => $value['modo'],
                                                            "fr"   => $value['fr'],
                                                            "sa"   => $value['sa'],
                                                            "so"   => $value['so'],
                                                        ]);
            }

            return redirect()->back();
        }


        public function addRowHz(Request $request){


            $tarif = new RechnerTarif();
            $tarif->tour = $request->tour_id;
            $tarif->jahr = now()->year;
            $tarif->modo = 0;
            $tarif->fr = 0;
            $tarif->sa = 0;
            $tarif->so = 0;
            $tarif->save();

            return response()->json(['id' => $tarif->id]);
        }


        public function removeRowHz(Request $request){

            $tarif = RechnerTarif::where('id', $request->id)->delete();


            return response()->json(['id' => $tarif]);
        }

    }

<?php

    use App\Http\Controllers\Backend\TarifController;

    use App\Http\Controllers\Old\Backend\AssignmentController;
    use App\Http\Controllers\Old\Backend\PriceController;
    use App\Http\Controllers\Old\Backend\UserPanelController;
    use App\Models\RechnerTouren;
    use Illuminate\Support\Facades\Route;


    Route::get('/', function(){
        return redirect('/backend/beilagenrechner');
    });

    Route::get('/buss', [\App\Http\Controllers\Backend\BackendPageController::class, 'index'])->name('backend.beilagenrechner.buss');
    Route::get('/buss/dashboard', [\App\Http\Controllers\Backend\buss\BackendBussPageController::class, 'dashboard'])->name('backend.beilagenrechner.dashboard');

    //Planungskalender
    Route::get('/buss/planungskalender/create/{objekt?}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'create'])->name('backend.beilagenrechner.planungskalender.create');
    Route::get('/buss/planungskalender/{storno?}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'index'])->name('backend.beilagenrechner.planungskalender')->withTrashed();
    Route::get('/buss/planungskalender/edit/{id}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'edit'])->name('backend.beilagenrechner.planungskalender.edit')->withTrashed();
    Route::get('/buss/planungskalender/storno/{id}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'storno'])->name('backend.beilagenrechner.planungskalender.storno')->withTrashed();
    Route::get('/buss/planungskalender/storno-confirm/{id}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'storno_confirmed'])->name('backend.beilagenrechner.planungskalender.storno-confirm')->withTrashed();
    Route::get('/buss/planungskalender/detail/{auftrag}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'detail'])->name('backend.beilagenrechner.planungskalender.detail')->withTrashed();
    Route::get('/buss/planungskalender/restore/{id}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'restore'])->name('backend.beilagenrechner.planungskalender.restore')->withTrashed();
    Route::get('/buss/planungskalender/restore-confirm/{id}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'restore_confirmed'])->name('backend.beilagenrechner.planungskalender.restore-confirm')->withTrashed();
    Route::post('/buss/planungskalender/update/{auftrag}', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'update'])->name('backend.beilagenrechner.planungskalender.update')->withTrashed();
    Route::post('/buss/planungskalender/tagesnotiz', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'saveTagesnotiz'])->name('backend.beilagenrechner.planungskalender.tagesnotiz');
    Route::post('/buss/planungskalender/{id}/save-notiz', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'saveNotiz'])->name('backend.beilagenrechner.planungskalender.save-notiz');

    Route::post('/buss/planungskalender/copy-to-dates', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'copyToDates'])->name('backend.beilagenrechner.planungskalender.copy-to-dates');
    Route::post('/buss/planungskalender/copy-to-weekdays', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'copyToWeekdays']);



    //Erfassungsmaske
    Route::get('/buss/erfassungsmaske/create', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'create'])->name('backend.beilagenrechner.erfassungsmaske.create');
    Route::get('/buss/erfassungsmaske/{storno?}', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'index'])->name('backend.beilagenrechner.erfassungsmaske');
    Route::get('/buss/erfassungsmaske/edit/{id}', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'edit'])->name('backend.beilagenrechner.erfassungsmaske.edit');
    Route::get('/buss/erfassungsmaske/storno/{id}', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'storno'])->name('backend.beilagenrechner.erfassungsmaske.storno')->withTrashed();
    Route::get('/buss/erfassungsmaske/storno-confirm/{id}', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'storno_confirmed'])->name('backend.beilagenrechner.erfassungsmaske.storno-confirm')->withTrashed();
    Route::get('/buss/erfassungsmaske/restore/{id}', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'restore'])->name('backend.beilagenrechner.erfassungsmaske.restore')->withTrashed();
    Route::get('/buss/erfassungsmaske/restore-confirm/{id}', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'restore_confirmed'])->name('backend.beilagenrechner.erfassungsmaske.restore-confirm')->withTrashed();
    Route::post('/buss/erfassungsmaske/{id}/save-notiz', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'saveNotiz'])->name('backend.beilagenrechner.erfassungsmaske.save-notiz');
    Route::post('/buss/erfassungsmaske/update/{id}', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'update'])->name('backend.beilagenrechner.erfassungsmaske.update');
    Route::get('/buss/erfassungsmaske/get/planungen', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'getPlanungen'])->name('backend.beilagenrechner.erfassungsmaske.getPlanungen')->withTrashed();
    Route::get('/buss/erfassungsmaske/get/zuweisung', [\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'zuweisung'])->name('backend.beilagenrechner.erfassungsmaske.zuweisen')->withTrashed();
    Route::post('/buss/planungskalender/transfer-to-kurier', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'transferToKurier']);


    Route::get('/beilagenrechner', [\App\Http\Controllers\Backend\BackendPageController::class, 'index'])->name('backend.beilagenrechner');
    Route::get('/beilagenrechner/dashboard', [\App\Http\Controllers\Backend\BackendPageController::class, 'uebersicht']);
    Route::get('/beilagenrechner/tarife', [\App\Http\Controllers\Backend\BackendPageController::class, 'tarife']);
    Route::get('/beilagenrechner/tarifzonen', [\App\Http\Controllers\Backend\BackendPageController::class, 'tarifzonen']);
    Route::get('/beilagenrechner/posttarife', [\App\Http\Controllers\Backend\BackendPageController::class, 'posttarife']);
    Route::get('/beilagenrechner/kategorien', [\App\Http\Controllers\Backend\BackendPageController::class, 'kategorien']);
    Route::get('/beilagenrechner/plz', [\App\Http\Controllers\Backend\BackendPageController::class, 'postleitzahlen']);
    Route::get('/beilagenrechner/touren', [\App\Http\Controllers\Backend\BackendPageController::class, 'touren']);
    Route::get('/beilagenrechner/touren_hz', [\App\Http\Controllers\Backend\BackendPageController::class, 'tourenHz']);

    Route::get('/login', [\App\Http\Controllers\Backend\BackendPageController::class, 'login']);


    //Tarif
    Route::post('/beilagenrechner/tarif/create', [\App\Http\Controllers\Backend\TarifController::class, 'create']);
    Route::get('/beilagenrechner/tarif/{id}/delete', [\App\Http\Controllers\Backend\TarifController::class, 'delete']);
    Route::get('/beilagenrechner/tarif/{id}/edit', [\App\Http\Controllers\Backend\TarifController::class, 'edit']);
    Route::post('/beilagenrechner/tarif/update', [\App\Http\Controllers\Backend\TarifController::class, 'update']);
    Route::get('/beilagenrechner/tarif/{id}/copy', [\App\Http\Controllers\Backend\TarifController::class, 'copy']);


    //Tarifzonen
    Route::post('/beilagenrechner/tarifzone/create', [\App\Http\Controllers\Backend\TarifzonenController::class, 'create']);
    Route::get('/beilagenrechner/tarifzone/{id}/delete', [\App\Http\Controllers\Backend\TarifzonenController::class, 'delete']);
    Route::get('/beilagenrechner/tarifzone/{id}/edit', [\App\Http\Controllers\Backend\TarifzonenController::class, 'edit']);
    Route::post('/beilagenrechner/tarifzone/update', [\App\Http\Controllers\Backend\TarifzonenController::class, 'update']);
    Route::get('/beilagenrechner/tarifzone/{id}/copy', [\App\Http\Controllers\Backend\TarifzonenController::class, 'copy']);

    //Posttarife
    Route::post('/beilagenrechner/posttarif/create', [\App\Http\Controllers\Backend\PosttarifeController::class, 'create']);
    Route::get('/beilagenrechner/posttarif/{id}/delete', [\App\Http\Controllers\Backend\PosttarifeController::class, 'delete']);
    Route::get('/beilagenrechner/posttarif/{id}/edit', [\App\Http\Controllers\Backend\PosttarifeController::class, 'edit']);
    Route::post('/beilagenrechner/posttarif/update', [\App\Http\Controllers\Backend\PosttarifeController::class, 'update']);
    Route::get('/beilagenrechner/posttarif/{id}/copy', [\App\Http\Controllers\Backend\PosttarifeController::class, 'copy']);

    //Kategorie
    Route::post('/beilagenrechner/kategorie/create', [\App\Http\Controllers\Backend\KategorieController::class, 'create']);
    Route::get('/beilagenrechner/kategorie/{id}/delete', [\App\Http\Controllers\Backend\KategorieController::class, 'delete']);
    Route::get('/beilagenrechner/kategorie/{id}/edit', [\App\Http\Controllers\Backend\KategorieController::class, 'edit']);
    Route::post('/beilagenrechner/kategorie/update', [\App\Http\Controllers\Backend\KategorieController::class, 'update']);
    Route::get('/beilagenrechner/kategorie/{id}/copy', [\App\Http\Controllers\Backend\KategorieController::class, 'copy']);

    //PLZ
    Route::post('/beilagenrechner/plz/create', [\App\Http\Controllers\Backend\PostleitzahlenController::class, 'create']);
    Route::get('/beilagenrechner/plz/{id}/delete', [\App\Http\Controllers\Backend\PostleitzahlenController::class, 'delete']);
    Route::get('/beilagenrechner/plz/{id}/edit', [\App\Http\Controllers\Backend\PostleitzahlenController::class, 'edit']);
    Route::post('/beilagenrechner/plz/update', [\App\Http\Controllers\Backend\PostleitzahlenController::class, 'update']);
    Route::get('/beilagenrechner/plz/{id}/copy', [\App\Http\Controllers\Backend\PostleitzahlenController::class, 'copy']);



    //Touren
    Route::get('/beilagenrechner/touren/create', [\App\Http\Controllers\Backend\TourenController::class, 'create'])->name('create.touren');
    Route::get('/beilagenrechner/touren/{tour}/edit', [\App\Http\Controllers\Backend\TourenController::class, 'edit'])->name('edit.touren');
    Route::post('/beilagenrechner/touren/{tour}/update', [\App\Http\Controllers\Backend\TourenController::class, 'update'])->name('tours.update');
    Route::post('/beilagenrechner/touren/edit/addRow', [\App\Http\Controllers\Backend\TourenController::class, 'addRow'])->name('edit.touren.addRow');
    Route::post('/beilagenrechner/touren/removeRow/tarif', [\App\Http\Controllers\Backend\TourenController::class, 'removeRow'])->name('edit.touren.removeRow');

    //Touren HU
    Route::get('/beilagenrechner/touren_hz/{tour}/edit', [\App\Http\Controllers\Backend\TourenController::class, 'editHz'])->name('edit.touren_hz');
    Route::get('/beilagenrechner/touren_hz/create', [\App\Http\Controllers\Backend\TourenController::class, 'createHz'])->name('create.touren_hz');
    Route::post('/beilagenrechner/touren_hz/{tour}/update', [\App\Http\Controllers\Backend\TourenController::class, 'updateHz'])->name('tours_hz.update');
    Route::post('/beilagenrechner/touren_hz/edit/addRow', [\App\Http\Controllers\Backend\TourenController::class, 'addRowHz'])->name('edit.touren_hz.addRow');
    Route::post('/beilagenrechner/touren_hz/removeRow/tarif', [\App\Http\Controllers\Backend\TourenController::class, 'removeRowHz'])->name('edit.touren_hz.removeRow');


    //Ajax
    Route::get('/buss/erfassungsmaske/edit/planungen/suchen', [\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'suche'])->name('planungen.suchen');
    Route::get('/buss/erfassungsmaske/edit/planungen/zuweisung/delete/{id}',[\App\Http\Controllers\Backend\buss\PlanungskalenderController::class, 'zuweisungDelete'])->name('zuweisung.delete');
    Route::post('/buss/erfassungsmaske/edit/touren/zuweisung',[\App\Http\Controllers\Backend\buss\ErfassungsmaskeController::class, 'tourenZuweisung'])->name('touren.zuweisung');



  /*  Route::get('/beilagenrechner/touren/{tour}/edit', function ($id) {
        dd('Route matched: ' . $id);
    })->name('edit.touren');*/
